annotated-types==0.7.0
anyio==4.9.0
cachetools==5.5.2
certifi==2025.7.14
charset-normalizer==3.4.2
google-ai-generativelanguage==0.6.15
google-api-core==2.25.1
google-api-python-client==2.177.0
google-auth==2.40.3
google-auth-httplib2==0.2.0
google-generativeai==0.8.5
googleapis-common-protos==1.70.0
grpcio==1.74.0
grpcio-status==1.71.2
h11==0.16.0
httpcore==1.0.9
httplib2==0.22.0
httpx==0.28.1
idna==3.10
iniconfig==2.1.0
numpy==2.2.6
ollama==0.5.1
opencv-python==*********
packaging==25.0
pillow==11.3.0
pluggy==1.6.0
proto-plus==1.26.1
protobuf==5.29.5
pyasn1==0.6.1
pyasn1_modules==0.4.2
pydantic==2.11.7
pydantic_core==2.33.2
Pygments==2.19.2
pyobjc==11.1
pyobjc-core==11.1
pyobjc-framework-Accessibility==11.1
pyobjc-framework-Accounts==11.1
pyobjc-framework-AddressBook==11.1
pyobjc-framework-AdServices==11.1
pyobjc-framework-AdSupport==11.1
pyobjc-framework-AppleScriptKit==11.1
pyobjc-framework-AppleScriptObjC==11.1
pyobjc-framework-ApplicationServices==11.1
pyobjc-framework-AppTrackingTransparency==11.1
pyobjc-framework-AudioVideoBridging==11.1
pyobjc-framework-AuthenticationServices==11.1
pyobjc-framework-AutomaticAssessmentConfiguration==11.1
pyobjc-framework-Automator==11.1
pyobjc-framework-AVFoundation==11.1
pyobjc-framework-AVKit==11.1
pyobjc-framework-AVRouting==11.1
pyobjc-framework-BackgroundAssets==11.1
pyobjc-framework-BrowserEngineKit==11.1
pyobjc-framework-BusinessChat==11.1
pyobjc-framework-CalendarStore==11.1
pyobjc-framework-CallKit==11.1
pyobjc-framework-Carbon==11.1
pyobjc-framework-CFNetwork==11.1
pyobjc-framework-Cinematic==11.1
pyobjc-framework-ClassKit==11.1
pyobjc-framework-CloudKit==11.1
pyobjc-framework-Cocoa==11.1
pyobjc-framework-Collaboration==11.1
pyobjc-framework-ColorSync==11.1
pyobjc-framework-Contacts==11.1
pyobjc-framework-ContactsUI==11.1
pyobjc-framework-CoreAudio==11.1
pyobjc-framework-CoreAudioKit==11.1
pyobjc-framework-CoreBluetooth==11.1
pyobjc-framework-CoreData==11.1
pyobjc-framework-CoreHaptics==11.1
pyobjc-framework-CoreLocation==11.1
pyobjc-framework-CoreMedia==11.1
pyobjc-framework-CoreMediaIO==11.1
pyobjc-framework-CoreMIDI==11.1
pyobjc-framework-CoreML==11.1
pyobjc-framework-CoreMotion==11.1
pyobjc-framework-CoreServices==11.1
pyobjc-framework-CoreSpotlight==11.1
pyobjc-framework-CoreText==11.1
pyobjc-framework-CoreWLAN==11.1
pyobjc-framework-CryptoTokenKit==11.1
pyobjc-framework-DataDetection==11.1
pyobjc-framework-DeviceCheck==11.1
pyobjc-framework-DeviceDiscoveryExtension==11.1
pyobjc-framework-DictionaryServices==11.1
pyobjc-framework-DiscRecording==11.1
pyobjc-framework-DiscRecordingUI==11.1
pyobjc-framework-DiskArbitration==11.1
pyobjc-framework-DVDPlayback==11.1
pyobjc-framework-EventKit==11.1
pyobjc-framework-ExceptionHandling==11.1
pyobjc-framework-ExecutionPolicy==11.1
pyobjc-framework-ExtensionKit==11.1
pyobjc-framework-ExternalAccessory==11.1
pyobjc-framework-FileProvider==11.1
pyobjc-framework-FileProviderUI==11.1
pyobjc-framework-FinderSync==11.1
pyobjc-framework-FSEvents==11.1
pyobjc-framework-FSKit==11.1
pyobjc-framework-GameCenter==11.1
pyobjc-framework-GameController==11.1
pyobjc-framework-GameKit==11.1
pyobjc-framework-GameplayKit==11.1
pyobjc-framework-HealthKit==11.1
pyobjc-framework-ImageCaptureCore==11.1
pyobjc-framework-InputMethodKit==11.1
pyobjc-framework-InstallerPlugins==11.1
pyobjc-framework-InstantMessage==11.1
pyobjc-framework-Intents==11.1
pyobjc-framework-IntentsUI==11.1
pyobjc-framework-IOBluetooth==11.1
pyobjc-framework-IOBluetoothUI==11.1
pyobjc-framework-IOSurface==11.1
pyobjc-framework-iTunesLibrary==11.1
pyobjc-framework-KernelManagement==11.1
pyobjc-framework-LatentSemanticMapping==11.1
pyobjc-framework-LaunchServices==11.1
pyobjc-framework-libdispatch==11.1
pyobjc-framework-libxpc==11.1
pyobjc-framework-LinkPresentation==11.1
pyobjc-framework-LocalAuthentication==11.1
pyobjc-framework-LocalAuthenticationEmbeddedUI==11.1
pyobjc-framework-MailKit==11.1
pyobjc-framework-MapKit==11.1
pyobjc-framework-MediaAccessibility==11.1
pyobjc-framework-MediaExtension==11.1
pyobjc-framework-MediaLibrary==11.1
pyobjc-framework-MediaPlayer==11.1
pyobjc-framework-MediaToolbox==11.1
pyobjc-framework-Metal==11.1
pyobjc-framework-MetalFX==11.1
pyobjc-framework-MetalKit==11.1
pyobjc-framework-MetalPerformanceShaders==11.1
pyobjc-framework-MetalPerformanceShadersGraph==11.1
pyobjc-framework-MetricKit==11.1
pyobjc-framework-MLCompute==11.1
pyobjc-framework-ModelIO==11.1
pyobjc-framework-MultipeerConnectivity==11.1
pyobjc-framework-NaturalLanguage==11.1
pyobjc-framework-NetFS==11.1
pyobjc-framework-Network==11.1
pyobjc-framework-NetworkExtension==11.1
pyobjc-framework-NotificationCenter==11.1
pyobjc-framework-OpenDirectory==11.1
pyobjc-framework-OSAKit==11.1
pyobjc-framework-OSLog==11.1
pyobjc-framework-PassKit==11.1
pyobjc-framework-PencilKit==11.1
pyobjc-framework-PHASE==11.1
pyobjc-framework-Photos==11.1
pyobjc-framework-PhotosUI==11.1
pyobjc-framework-PreferencePanes==11.1
pyobjc-framework-PushKit==11.1
pyobjc-framework-Quartz==11.1
pyobjc-framework-QuickLookThumbnailing==11.1
pyobjc-framework-ReplayKit==11.1
pyobjc-framework-SafariServices==11.1
pyobjc-framework-SafetyKit==11.1
pyobjc-framework-SceneKit==11.1
pyobjc-framework-ScreenCaptureKit==11.1
pyobjc-framework-ScreenSaver==11.1
pyobjc-framework-ScreenTime==11.1
pyobjc-framework-ScriptingBridge==11.1
pyobjc-framework-SearchKit==11.1
pyobjc-framework-Security==11.1
pyobjc-framework-SecurityFoundation==11.1
pyobjc-framework-SecurityInterface==11.1
pyobjc-framework-SecurityUI==11.1
pyobjc-framework-SensitiveContentAnalysis==11.1
pyobjc-framework-ServiceManagement==11.1
pyobjc-framework-SharedWithYou==11.1
pyobjc-framework-SharedWithYouCore==11.1
pyobjc-framework-ShazamKit==11.1
pyobjc-framework-Social==11.1
pyobjc-framework-SoundAnalysis==11.1
pyobjc-framework-Speech==11.1
pyobjc-framework-SpriteKit==11.1
pyobjc-framework-StoreKit==11.1
pyobjc-framework-Symbols==11.1
pyobjc-framework-SyncServices==11.1
pyobjc-framework-SystemConfiguration==11.1
pyobjc-framework-SystemExtensions==11.1
pyobjc-framework-ThreadNetwork==11.1
pyobjc-framework-UniformTypeIdentifiers==11.1
pyobjc-framework-UserNotifications==11.1
pyobjc-framework-UserNotificationsUI==11.1
pyobjc-framework-VideoSubscriberAccount==11.1
pyobjc-framework-VideoToolbox==11.1
pyobjc-framework-Virtualization==11.1
pyobjc-framework-Vision==11.1
pyobjc-framework-WebKit==11.1
pyparsing==3.2.3
pytest==8.4.1
python-dotenv==1.1.1
pyttsx3==2.99
requests==2.32.4
rsa==4.9.1
sniffio==1.3.1
tqdm==4.67.1
typing-inspection==0.4.1
typing_extensions==4.14.1
uritemplate==4.2.0
urllib3==2.5.0
SpeechRecognition==3.10.4
